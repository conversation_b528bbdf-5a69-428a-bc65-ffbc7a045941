#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Frida Tool助手 - 现代化版本
用于管理Android设备上的Frida服务器

Copyright (c) 2025 By.举个🌰
All rights reserved.
"""

import tkinter as tk
from tkinter import messagebox, filedialog
import ttkbootstrap as ttk
from ttkbootstrap.constants import *
import subprocess
import threading
import os
import time
from datetime import datetime


class FridaToolAug:
    def __init__(self, root):
        self.root = root
        self.root.title("Frida Tool助手 Aug - By.举个🌰")
        self.root.geometry("700x600")
        self.root.resizable(True, True)
        
        # 设置现代化主题
        self.style = ttk.Style("superhero")  # 使用superhero主题，现代化深色主题
        
        # 创建主容器
        self.create_widgets()
        
        # 初始化状态
        self.adb_connected = False
        self.frida_running = False
        
        # 设置默认值
        self.ip_var.set("*************")
        self.port_var.set("5555")
        
        self.log_message("Frida Tool助手 Aug 已启动")
        self.log_message("版权所有 © 2025 By.举个🌰")

    def create_widgets(self):
        # 创建主框架
        main_frame = ttk.Frame(self.root, padding=20)
        main_frame.pack(fill=BOTH, expand=True)
        
        # 标题
        title_label = ttk.Label(
            main_frame, 
            text="Frida Tool助手 Aug", 
            font=("Arial", 18, "bold"),
            bootstyle="primary"
        )
        title_label.pack(pady=(0, 20))
        
        # ADB连接区域
        self.create_connection_frame(main_frame)
        
        # Frida管理区域
        self.create_frida_frame(main_frame)
        
        # 日志区域
        self.create_log_frame(main_frame)
        
        # 状态栏
        self.create_status_bar(main_frame)

    def create_connection_frame(self, parent):
        # ADB连接框架
        conn_frame = ttk.LabelFrame(parent, text="ADB连接设置", padding=15, bootstyle="info")
        conn_frame.pack(fill=X, pady=(0, 15))
        
        # IP地址输入
        ip_frame = ttk.Frame(conn_frame)
        ip_frame.pack(fill=X, pady=5)
        
        ttk.Label(ip_frame, text="IP地址:", width=10).pack(side=LEFT)
        self.ip_var = tk.StringVar()
        ip_entry = ttk.Entry(ip_frame, textvariable=self.ip_var, font=("Arial", 11))
        ip_entry.pack(side=LEFT, fill=X, expand=True, padx=(10, 0))
        
        # 端口输入
        port_frame = ttk.Frame(conn_frame)
        port_frame.pack(fill=X, pady=5)
        
        ttk.Label(port_frame, text="端口:", width=10).pack(side=LEFT)
        self.port_var = tk.StringVar()
        port_entry = ttk.Entry(port_frame, textvariable=self.port_var, font=("Arial", 11))
        port_entry.pack(side=LEFT, fill=X, expand=True, padx=(10, 0))
        
        # 连接按钮
        btn_frame = ttk.Frame(conn_frame)
        btn_frame.pack(fill=X, pady=(10, 0))
        
        self.connect_btn = ttk.Button(
            btn_frame, 
            text="连接ADB", 
            command=self.connect_adb_threaded,
            bootstyle="success",
            width=15
        )
        self.connect_btn.pack(side=LEFT, padx=(0, 10))
        
        self.disconnect_btn = ttk.Button(
            btn_frame, 
            text="断开连接", 
            command=self.disconnect_adb,
            bootstyle="secondary",
            width=15,
            state=DISABLED
        )
        self.disconnect_btn.pack(side=LEFT)
        
        # 连接状态指示器
        self.conn_status = ttk.Label(
            btn_frame, 
            text="未连接", 
            bootstyle="secondary",
            font=("Arial", 10, "bold")
        )
        self.conn_status.pack(side=RIGHT)

    def create_frida_frame(self, parent):
        # Frida管理框架
        frida_frame = ttk.LabelFrame(parent, text="Frida服务器管理", padding=15, bootstyle="warning")
        frida_frame.pack(fill=X, pady=(0, 15))
        
        # 按钮区域
        btn_frame = ttk.Frame(frida_frame)
        btn_frame.pack(fill=X, pady=5)
        
        self.upload_btn = ttk.Button(
            btn_frame, 
            text="上传Frida服务器", 
            command=self.upload_frida_threaded,
            bootstyle="info",
            width=20
        )
        self.upload_btn.pack(side=LEFT, padx=(0, 10))
        
        self.start_btn = ttk.Button(
            btn_frame, 
            text="启动Frida服务", 
            command=self.start_frida_threaded,
            bootstyle="success",
            width=20
        )
        self.start_btn.pack(side=LEFT, padx=(0, 10))
        
        self.stop_btn = ttk.Button(
            btn_frame, 
            text="停止Frida服务", 
            command=self.stop_frida_threaded,
            bootstyle="danger",
            width=20,
            state=DISABLED
        )
        self.stop_btn.pack(side=LEFT)
        
        # Frida状态
        status_frame = ttk.Frame(frida_frame)
        status_frame.pack(fill=X, pady=(10, 0))
        
        ttk.Label(status_frame, text="Frida状态:", font=("Arial", 10, "bold")).pack(side=LEFT)
        self.frida_status = ttk.Label(
            status_frame, 
            text="未运行", 
            bootstyle="secondary",
            font=("Arial", 10, "bold")
        )
        self.frida_status.pack(side=LEFT, padx=(10, 0))

    def create_log_frame(self, parent):
        # 日志区域
        log_frame = ttk.LabelFrame(parent, text="操作日志", padding=15, bootstyle="dark")
        log_frame.pack(fill=BOTH, expand=True, pady=(0, 15))
        
        # 创建文本框和滚动条
        text_frame = ttk.Frame(log_frame)
        text_frame.pack(fill=BOTH, expand=True)
        
        self.log_text = tk.Text(
            text_frame, 
            height=10, 
            wrap=tk.WORD,
            font=("Consolas", 9),
            bg="#2b2b2b",
            fg="#ffffff",
            insertbackground="#ffffff"
        )
        
        scrollbar = ttk.Scrollbar(text_frame, orient=VERTICAL, command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=scrollbar.set)
        
        self.log_text.pack(side=LEFT, fill=BOTH, expand=True)
        scrollbar.pack(side=RIGHT, fill=Y)
        
        # 清除日志按钮
        clear_btn = ttk.Button(
            log_frame, 
            text="清除日志", 
            command=self.clear_log,
            bootstyle="outline-secondary",
            width=15
        )
        clear_btn.pack(pady=(10, 0))

    def create_status_bar(self, parent):
        # 状态栏
        self.status_var = tk.StringVar()
        self.status_var.set("就绪")
        
        status_bar = ttk.Label(
            parent, 
            textvariable=self.status_var, 
            relief=tk.SUNKEN,
            anchor=tk.W,
            padding=5,
            bootstyle="inverse-secondary"
        )
        status_bar.pack(fill=X, side=BOTTOM)

    def log_message(self, message):
        """添加日志消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"
        
        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)
        self.root.update_idletasks()

    def clear_log(self):
        """清除日志"""
        self.log_text.delete(1.0, tk.END)
        self.log_message("日志已清除")

    def update_status(self, message):
        """更新状态栏"""
        self.status_var.set(message)
        self.root.update_idletasks()

    def connect_adb_threaded(self):
        """在线程中连接ADB"""
        threading.Thread(target=self.connect_adb, daemon=True).start()

    def connect_adb(self):
        """连接ADB"""
        ip = self.ip_var.get().strip()
        port = self.port_var.get().strip()
        
        if not ip or not port:
            messagebox.showwarning("警告", "请输入IP地址和端口")
            return
        
        try:
            self.update_status("正在连接ADB...")
            self.log_message(f"尝试连接到 {ip}:{port}")
            
            # 先断开现有连接
            subprocess.run(["adb", "disconnect"], capture_output=True, text=True, timeout=10)
            
            # 连接到指定设备
            result = subprocess.run(
                ["adb", "connect", f"{ip}:{port}"], 
                capture_output=True, 
                text=True, 
                timeout=15
            )
            
            if result.returncode == 0 and "connected" in result.stdout.lower():
                self.adb_connected = True
                self.conn_status.config(text="已连接", bootstyle="success")
                self.connect_btn.config(state=DISABLED)
                self.disconnect_btn.config(state=NORMAL)
                self.update_status("ADB连接成功")
                self.log_message("ADB连接成功")
                messagebox.showinfo("成功", "ADB连接成功")
            else:
                raise subprocess.CalledProcessError(result.returncode, result.args, result.stdout, result.stderr)
                
        except subprocess.TimeoutExpired:
            self.log_message("ADB连接超时")
            self.update_status("连接超时")
            messagebox.showerror("错误", "ADB连接超时，请检查网络连接")
        except subprocess.CalledProcessError as e:
            self.log_message(f"ADB连接失败: {e.stderr if e.stderr else e.stdout}")
            self.update_status("ADB连接失败")
            messagebox.showerror("错误", f"ADB连接失败:\n{e.stderr if e.stderr else e.stdout}")
        except Exception as e:
            self.log_message(f"连接异常: {str(e)}")
            self.update_status("连接异常")
            messagebox.showerror("错误", f"连接异常: {str(e)}")

    def disconnect_adb(self):
        """断开ADB连接"""
        try:
            subprocess.run(["adb", "disconnect"], capture_output=True, text=True, timeout=10)
            self.adb_connected = False
            self.conn_status.config(text="未连接", bootstyle="secondary")
            self.connect_btn.config(state=NORMAL)
            self.disconnect_btn.config(state=DISABLED)
            self.update_status("已断开连接")
            self.log_message("ADB连接已断开")
        except Exception as e:
            self.log_message(f"断开连接异常: {str(e)}")

    def upload_frida_threaded(self):
        """在线程中上传Frida服务器"""
        threading.Thread(target=self.upload_frida, daemon=True).start()

    def upload_frida(self):
        """上传Frida服务器"""
        if not self.adb_connected:
            messagebox.showwarning("警告", "请先连接ADB")
            return

        # 选择frida-server文件
        file_path = filedialog.askopenfilename(
            title="选择frida-server文件",
            filetypes=[
                ("可执行文件", "frida-server*"),
                ("所有文件", "*.*")
            ]
        )

        if not file_path:
            return

        try:
            self.update_status("正在上传Frida服务器...")
            self.log_message(f"开始上传: {os.path.basename(file_path)}")

            # 上传到临时目录
            result = subprocess.run(
                ["adb", "push", file_path, "/data/local/tmp/frida-server-temp"],
                capture_output=True,
                text=True,
                timeout=60
            )

            if result.returncode != 0:
                raise subprocess.CalledProcessError(result.returncode, result.args, result.stdout, result.stderr)

            self.log_message("文件上传完成，正在设置权限...")

            # 移动到目标位置并设置权限
            commands = [
                "adb shell su -c 'mv /data/local/tmp/frida-server-temp /data/local/frida-server'",
                "adb shell su -c 'chmod 777 /data/local/frida-server'"
            ]

            for cmd in commands:
                result = subprocess.run(cmd.split(), capture_output=True, text=True, timeout=30)
                if result.returncode != 0:
                    self.log_message(f"命令执行失败: {cmd}")
                    self.log_message(f"错误信息: {result.stderr}")
                    # 尝试不使用su权限
                    if "su" in cmd:
                        alt_cmd = cmd.replace("su -c ", "").replace("'", "")
                        self.log_message(f"尝试替代命令: {alt_cmd}")
                        result = subprocess.run(alt_cmd.split(), capture_output=True, text=True, timeout=30)
                        if result.returncode != 0:
                            raise subprocess.CalledProcessError(result.returncode, result.args, result.stdout, result.stderr)

            self.update_status("Frida服务器上传成功")
            self.log_message("Frida服务器上传并配置完成")
            messagebox.showinfo("成功", "Frida服务器上传成功")

        except subprocess.TimeoutExpired:
            self.log_message("上传超时")
            self.update_status("上传超时")
            messagebox.showerror("错误", "上传超时，请检查网络连接和设备状态")
        except subprocess.CalledProcessError as e:
            error_msg = e.stderr if e.stderr else e.stdout
            self.log_message(f"上传失败: {error_msg}")
            self.update_status("上传失败")
            messagebox.showerror("错误", f"上传失败:\n{error_msg}")
        except Exception as e:
            self.log_message(f"上传异常: {str(e)}")
            self.update_status("上传异常")
            messagebox.showerror("错误", f"上传异常: {str(e)}")

    def start_frida_threaded(self):
        """在线程中启动Frida服务"""
        threading.Thread(target=self.start_frida, daemon=True).start()

    def start_frida(self):
        """启动Frida服务器"""
        if not self.adb_connected:
            messagebox.showwarning("警告", "请先连接ADB")
            return

        try:
            self.update_status("正在检查Frida服务器...")
            self.log_message("检查设备上的Frida服务器...")

            # 查找所有frida相关文件
            result = subprocess.run(
                ["adb", "shell", "find", "/data/local", "-name", "*frida*", "-type", "f"],
                capture_output=True,
                text=True,
                timeout=30
            )

            if result.returncode != 0:
                # 尝试简单的ls命令
                result = subprocess.run(
                    ["adb", "shell", "ls", "/data/local/frida*"],
                    capture_output=True,
                    text=True,
                    timeout=30
                )

            servers = [line.strip() for line in result.stdout.splitlines() if line.strip() and 'frida' in line.lower()]

            if not servers:
                messagebox.showwarning("警告", "未找到Frida服务器文件，请先上传")
                return

            selected_server = None

            if len(servers) == 1:
                selected_server = servers[0]
                self.log_message(f"找到Frida服务器: {selected_server}")
            else:
                # 多个服务器，让用户选择
                self.log_message(f"找到多个Frida服务器: {len(servers)}个")
                selected_server = self.select_frida_server(servers)

            if not selected_server:
                return

            self.log_message(f"准备启动: {selected_server}")
            self.update_status("正在启动Frida服务器...")

            # 确保权限正确
            subprocess.run(
                ["adb", "shell", "su", "-c", f"chmod 777 {selected_server}"],
                capture_output=True,
                text=True,
                timeout=15
            )

            # 启动frida服务器
            start_cmd = ["adb", "shell", "su", "-c", f"nohup {selected_server} > /dev/null 2>&1 &"]
            result = subprocess.run(start_cmd, capture_output=True, text=True, timeout=15)

            # 等待一下让服务启动
            time.sleep(2)

            # 检查服务是否启动
            check_result = subprocess.run(
                ["adb", "shell", "ps", "|", "grep", "frida"],
                capture_output=True,
                text=True,
                timeout=10
            )

            if "frida" in check_result.stdout.lower():
                self.frida_running = True
                self.frida_status.config(text="运行中", bootstyle="success")
                self.start_btn.config(state=DISABLED)
                self.stop_btn.config(state=NORMAL)
                self.update_status("Frida服务器启动成功")
                self.log_message("Frida服务器启动成功")
                messagebox.showinfo("成功", "Frida服务器启动成功")
            else:
                self.log_message("Frida服务器可能启动失败，请检查设备状态")
                self.update_status("启动状态未知")
                messagebox.showwarning("警告", "Frida服务器启动状态未知，请手动检查")

        except subprocess.TimeoutExpired:
            self.log_message("启动超时")
            self.update_status("启动超时")
            messagebox.showerror("错误", "启动超时")
        except Exception as e:
            self.log_message(f"启动异常: {str(e)}")
            self.update_status("启动异常")
            messagebox.showerror("错误", f"启动异常: {str(e)}")

    def select_frida_server(self, servers):
        """选择Frida服务器对话框"""
        selection_window = tk.Toplevel(self.root)
        selection_window.title("选择Frida服务器")
        selection_window.geometry("500x300")
        selection_window.transient(self.root)
        selection_window.grab_set()

        # 居中显示
        selection_window.geometry("+%d+%d" % (
            self.root.winfo_rootx() + 100,
            self.root.winfo_rooty() + 100
        ))

        selected_server = None

        # 标题
        ttk.Label(
            selection_window,
            text="发现多个Frida服务器，请选择要启动的版本:",
            font=("Arial", 12, "bold")
        ).pack(pady=20)

        # 列表框
        listbox_frame = ttk.Frame(selection_window)
        listbox_frame.pack(fill=BOTH, expand=True, padx=20, pady=10)

        listbox = tk.Listbox(listbox_frame, font=("Consolas", 10))
        scrollbar = ttk.Scrollbar(listbox_frame, orient=VERTICAL, command=listbox.yview)
        listbox.configure(yscrollcommand=scrollbar.set)

        for server in servers:
            listbox.insert(tk.END, server)

        listbox.pack(side=LEFT, fill=BOTH, expand=True)
        scrollbar.pack(side=RIGHT, fill=Y)

        # 按钮
        button_frame = ttk.Frame(selection_window)
        button_frame.pack(pady=20)

        def on_select():
            nonlocal selected_server
            selection = listbox.curselection()
            if selection:
                selected_server = servers[selection[0]]
                selection_window.destroy()

        def on_cancel():
            selection_window.destroy()

        ttk.Button(button_frame, text="选择", command=on_select, bootstyle="success").pack(side=LEFT, padx=10)
        ttk.Button(button_frame, text="取消", command=on_cancel, bootstyle="secondary").pack(side=LEFT)

        # 双击选择
        listbox.bind("<Double-Button-1>", lambda e: on_select())

        # 等待窗口关闭
        selection_window.wait_window()

        return selected_server

    def stop_frida_threaded(self):
        """在线程中停止Frida服务"""
        threading.Thread(target=self.stop_frida, daemon=True).start()

    def stop_frida(self):
        """停止Frida服务器"""
        if not self.adb_connected:
            messagebox.showwarning("警告", "请先连接ADB")
            return

        try:
            self.update_status("正在停止Frida服务器...")
            self.log_message("停止Frida服务器...")

            # 杀死frida进程
            result = subprocess.run(
                ["adb", "shell", "su", "-c", "pkill -f frida"],
                capture_output=True,
                text=True,
                timeout=15
            )

            time.sleep(1)

            # 检查是否还有frida进程
            check_result = subprocess.run(
                ["adb", "shell", "ps", "|", "grep", "frida"],
                capture_output=True,
                text=True,
                timeout=10
            )

            if "frida" not in check_result.stdout.lower():
                self.frida_running = False
                self.frida_status.config(text="未运行", bootstyle="secondary")
                self.start_btn.config(state=NORMAL)
                self.stop_btn.config(state=DISABLED)
                self.update_status("Frida服务器已停止")
                self.log_message("Frida服务器已停止")
                messagebox.showinfo("成功", "Frida服务器已停止")
            else:
                self.log_message("Frida服务器可能仍在运行")
                self.update_status("停止状态未知")
                messagebox.showwarning("警告", "Frida服务器停止状态未知")

        except Exception as e:
            self.log_message(f"停止异常: {str(e)}")
            self.update_status("停止异常")
            messagebox.showerror("错误", f"停止异常: {str(e)}")


def main():
    """主函数"""
    root = ttk.Window(themename="superhero")
    app = FridaToolAug(root)

    # 设置窗口图标（如果有的话）
    try:
        root.iconbitmap("frida.ico")
    except:
        pass

    # 启动应用
    root.mainloop()


if __name__ == "__main__":
    main()
