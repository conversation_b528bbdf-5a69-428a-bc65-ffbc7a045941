# Frida Tool助手 Aug

一个现代化的Python GUI工具，用于管理Android设备上的Frida服务器。

**版权所有 © 2025 By.举个🌰**

## 功能特性

- 🔗 **ADB连接管理** - 通过IP地址和端口连接Android设备
- 📤 **Frida服务器上传** - 自动上传并配置Frida服务器
- 🚀 **智能服务管理** - 自动检测多版本Frida服务器并提供选择
- 🎨 **现代化UI** - 使用ttkbootstrap提供现代化深色主题
- 📝 **实时日志** - 详细的操作日志和状态反馈
- 🔒 **权限管理** - 自动处理su权限提升和文件权限设置

## 系统要求

- Python 3.7+
- ADB工具已安装并配置在系统PATH中
- Android设备已开启USB调试和网络ADB调试

## 安装依赖

### 自动安装（推荐）
工具会自动创建虚拟环境并安装依赖：

```bash
# 运行工具时会自动处理依赖
python Frida_Tool助手Aug.py
```

### 手动安装
如果需要手动安装依赖：

```bash
# 创建虚拟环境
python -m venv frida_tool_env

# 激活虚拟环境 (Windows)
frida_tool_env\Scripts\activate

# 激活虚拟环境 (Linux/Mac)
source frida_tool_env/bin/activate

# 安装依赖
pip install ttkbootstrap pillow
```

## 使用方法

### 1. 启动工具
```bash
# 在虚拟环境中运行
python Frida_Tool助手Aug.py
```

### 2. 连接Android设备
1. 在"ADB连接设置"区域输入设备的IP地址和端口
2. 点击"连接ADB"按钮
3. 等待连接成功提示

### 3. 上传Frida服务器
1. 确保ADB已连接
2. 点击"上传Frida服务器"按钮
3. 选择本地的frida-server文件
4. 工具会自动上传到`/data/local/tmp/frida-server`并设置权限

### 4. 启动Frida服务
1. 点击"启动Frida服务"按钮
2. 如果检测到多个frida-server版本，会弹出选择对话框
3. 选择要启动的版本
4. 工具会自动执行以下操作：
   - su权限提升
   - chmod 777设置执行权限
   - 后台启动/data/local/tmp/frida-server服务

### 5. 停止Frida服务
- 点击"停止Frida服务"按钮即可停止运行中的Frida服务器

## 界面说明

### ADB连接设置
- **IP地址**: Android设备的IP地址
- **端口**: ADB端口（通常是5555）
- **连接状态**: 显示当前连接状态

### Frida服务器管理
- **上传按钮**: 上传frida-server文件到设备
- **启动按钮**: 启动Frida服务器
- **停止按钮**: 停止Frida服务器
- **服务状态**: 显示Frida服务器运行状态

### 操作日志
- 显示所有操作的详细日志
- 包含时间戳和状态信息
- 可以清除日志内容

## 技术特性

### 多版本支持
- 自动检测设备上的所有frida-server文件
- 提供图形化选择界面
- 支持不同架构和版本的frida-server

### 权限处理
- 自动尝试su权限提升
- 如果su失败，会尝试普通权限操作
- 自动设置文件执行权限

### 错误处理
- 完善的异常捕获和处理
- 用户友好的错误提示
- 详细的日志记录

### 异步操作
- 所有网络和系统操作都在后台线程执行
- 避免UI冻结
- 实时状态更新

## 故障排除

### ADB连接失败
1. 确认设备IP地址和端口正确
2. 检查设备是否开启网络ADB调试
3. 确认防火墙没有阻止连接
4. 尝试使用`adb devices`命令检查设备状态

### 上传失败
1. 确认ADB连接正常
2. 检查设备存储空间是否充足
3. 确认设备root权限状态
4. 检查frida-server文件是否损坏

### 启动失败
1. 确认frida-server文件已正确上传
2. 检查设备是否已root
3. 确认没有其他frida进程在运行
4. 检查设备架构是否与frida-server匹配

## 开发信息

- **开发语言**: Python 3
- **GUI框架**: tkinter + ttkbootstrap
- **主题**: superhero (现代化深色主题)
- **版权**: © 2025 By.举个🌰

## 更新日志

### v1.0 (2025-01-30)
- 初始版本发布
- 现代化UI设计
- 完整的ADB连接管理
- 智能Frida服务器管理
- 多版本选择支持
- 详细日志记录

## 许可证

本软件仅供学习和研究使用。

**版权所有 © 2025 By.举个🌰**
